.App{
  height: 100vh;
}
.logo{
  width: 75px;
  height: 75px;
}
.navbar{
  display: flex;
  background-color: black;
  color: rgb(201, 201, 201)
}
.site-name{
  font-size: 1.5em;
  margin: auto 0;
}
.nav-list{
  list-style: none;
  display: flex;
  justify-content:  flex-end;
  flex-basis: 100%;
  margin: auto 3em;
}
a{
  text-decoration: none;
  color: inherit;
}

.nav-link{
 padding: 10px 20px;
 font-size: 1.2em;;
}
.flex{
  display: flex;
}
.side-panel{
  background-color: #61DBFB;
  color: white;
  flex-basis: 23.6%;;
}
.side-panel-list{
  list-style:none;
  text-align: center;
 margin: 0;
 padding: 0;
}
.side-panel-item{
  text-decoration: underline;
  padding: 1em 0;
}